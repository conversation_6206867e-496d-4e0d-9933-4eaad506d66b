"""
Integration tests for RDF operations and SPARQL queries.
Tests the complete flow: TTL data -> GraphDB -> SPARQL queries
"""
import pytest
import asyncio
from pathlib import Path
from typing import List, Dict, Any

from core.dependencies import get_dependencies, cleanup_dependencies
from agents.rdf_query_agent import RDFQueryAgent
from tests.utils.cleanup import TestDataCleanup
from tests.utils.helpers import TestDataGenerator, FileHelpers, AssertionHelpers


@pytest.mark.integration
@pytest.mark.requires_services
class TestRDFOperationsIntegration:
    """Integration tests for RDF operations and SPARQL queries."""
    
    @pytest.fixture(scope="class")
    async def dependencies(self):
        """Get real dependencies for integration testing."""
        deps = await get_dependencies()
        yield deps
        await cleanup_dependencies()
    
    @pytest.fixture(scope="class")
    async def cleanup_util(self, dependencies):
        """Create cleanup utility for test data."""
        return TestDataCleanup(
            dependencies.minio_client,
            dependencies.qdrant_client,
            dependencies.graphdb_client
        )
    
    @pytest.fixture(autouse=True)
    async def setup_and_cleanup(self, cleanup_util):
        """Setup and cleanup test data before and after each test."""
        # Cleanup before test
        await cleanup_util.graphdb_cleanup.cleanup_test_repository()
        yield
        # Cleanup after test
        await cleanup_util.graphdb_cleanup.cleanup_test_repository()
    
    @pytest.fixture
    async def sample_ttl_data(self, dependencies) -> str:
        """Create and load sample TTL data into GraphDB."""
        ttl_content = TestDataGenerator.generate_ttl_data(entity_count=10)
        
        # Load TTL data into GraphDB
        success = await dependencies.graphdb_client.load_ttl_data(ttl_content)
        assert success, "Failed to load TTL data into GraphDB"
        
        return ttl_content
    
    @pytest.fixture
    async def rdf_agent(self, dependencies):
        """Create RDF query agent for testing."""
        return RDFQueryAgent(dependencies)
    
    @pytest.mark.asyncio
    async def test_ttl_data_loading_and_querying(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test loading TTL data and executing SPARQL queries."""
        # Verify data was loaded
        count_query = """
        PREFIX ex: <http://example.org/>
        SELECT (COUNT(?building) as ?count) WHERE {
            ?building a ex:Building .
        }
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(count_query)
        assert result is not None
        assert "results" in result
        
        bindings = result["results"]["bindings"]
        assert len(bindings) > 0
        
        count = int(bindings[0]["count"]["value"])
        assert count == 10, f"Expected 10 buildings, found {count}"
    
    @pytest.mark.asyncio
    async def test_complex_sparql_queries(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test complex SPARQL queries with joins and filters."""
        # Query buildings with their addresses
        complex_query = """
        PREFIX ex: <http://example.org/>
        PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
        
        SELECT ?building ?label ?street ?city WHERE {
            ?building a ex:Building ;
                      rdfs:label ?label ;
                      ex:hasAddress ?address .
            ?address ex:street ?street ;
                     ex:city ?city .
            FILTER(?city = "Test City")
        }
        ORDER BY ?label
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(complex_query)
        assert result is not None
        
        bindings = result["results"]["bindings"]
        assert len(bindings) > 0
        
        # Verify all results have required fields
        for binding in bindings:
            assert "building" in binding
            assert "label" in binding
            assert "street" in binding
            assert "city" in binding
            assert binding["city"]["value"] == "Test City"
    
    @pytest.mark.asyncio
    async def test_rdf_agent_natural_language_processing(
        self, 
        rdf_agent: RDFQueryAgent, 
        sample_ttl_data: str
    ):
        """Test RDF agent processing natural language queries."""
        natural_queries = [
            "How many buildings are there?",
            "Find all buildings with energy rating A+",
            "What are the addresses of buildings in Test City?",
            "List all buildings and their energy ratings"
        ]
        
        for query in natural_queries:
            result = await rdf_agent.process_query(
                query=query,
                query_type="natural_language",
                session_id="test-session"
            )
            
            assert result is not None
            assert isinstance(result, str)
            assert len(result) > 0
            # Should contain relevant information
            assert any(keyword in result.lower() for keyword in ["building", "energy", "address"])
    
    @pytest.mark.asyncio
    async def test_rdf_agent_sparql_generation(
        self, 
        rdf_agent: RDFQueryAgent, 
        sample_ttl_data: str
    ):
        """Test RDF agent SPARQL query generation."""
        natural_query = "Find buildings with A+ energy rating"
        
        # Generate SPARQL from natural language
        sparql_query = await rdf_agent.generate_sparql_query(natural_query)
        
        assert sparql_query is not None
        assert AssertionHelpers.assert_valid_sparql(sparql_query)
        assert "SELECT" in sparql_query.upper()
        assert "Building" in sparql_query or "building" in sparql_query
        assert "A+" in sparql_query or "energyRating" in sparql_query
    
    @pytest.mark.asyncio
    async def test_rdf_agent_sparql_validation(
        self, 
        rdf_agent: RDFQueryAgent
    ):
        """Test RDF agent SPARQL validation."""
        # Valid SPARQL
        valid_query = """
        PREFIX ex: <http://example.org/>
        SELECT ?s WHERE { ?s a ex:Building }
        """
        
        is_valid = await rdf_agent.validate_sparql_query(valid_query)
        assert is_valid is True
        
        # Invalid SPARQL
        invalid_query = "This is not SPARQL"
        is_valid = await rdf_agent.validate_sparql_query(invalid_query)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_concurrent_sparql_queries(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test concurrent execution of multiple SPARQL queries."""
        queries = [
            "SELECT ?s WHERE { ?s a <http://example.org/Building> }",
            "SELECT ?s WHERE { ?s a <http://example.org/Address> }",
            "SELECT (COUNT(?s) as ?count) WHERE { ?s ?p ?o }",
            """SELECT ?building ?rating WHERE { 
                ?building <http://example.org/energyRating> ?rating 
            }"""
        ]
        
        # Execute all queries concurrently
        tasks = [
            dependencies.graphdb_client.execute_sparql_query(query)
            for query in queries
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check all queries succeeded
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Query {i} failed: {result}")
            assert result is not None
            assert "results" in result
    
    @pytest.mark.asyncio
    async def test_rdf_data_updates_and_queries(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test updating RDF data and querying updated data."""
        # Add new building data
        new_ttl = """
        @prefix ex: <http://example.org/> .
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        
        ex:NewBuilding rdf:type ex:Building ;
            rdfs:label "New Test Building" ;
            ex:energyRating "A++" ;
            ex:hasAddress ex:NewAddress .
        
        ex:NewAddress rdf:type ex:Address ;
            ex:street "999 New Street" ;
            ex:city "New City" ;
            ex:country "Test Country" .
        """
        
        # Load new data
        success = await dependencies.graphdb_client.load_ttl_data(new_ttl)
        assert success
        
        # Query for the new building
        query = """
        PREFIX ex: <http://example.org/>
        PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
        
        SELECT ?building ?label WHERE {
            ?building rdfs:label "New Test Building" ;
                      a ex:Building ;
                      rdfs:label ?label .
        }
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(query)
        assert result is not None
        
        bindings = result["results"]["bindings"]
        assert len(bindings) == 1
        assert bindings[0]["label"]["value"] == "New Test Building"
    
    @pytest.mark.asyncio
    async def test_rdf_data_deletion(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test deleting RDF data."""
        # Delete specific building
        delete_query = """
        PREFIX ex: <http://example.org/>
        
        DELETE WHERE {
            ex:Building0 ?p ?o .
        }
        """
        
        result = await dependencies.graphdb_client.execute_sparql_update(delete_query)
        assert result is not None
        
        # Verify deletion
        check_query = """
        PREFIX ex: <http://example.org/>
        
        ASK {
            ex:Building0 ?p ?o .
        }
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(check_query)
        assert result is not None
        assert result.get("boolean", True) is False
    
    @pytest.mark.asyncio
    async def test_rdf_inference_and_reasoning(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test RDF inference and reasoning capabilities."""
        # Add inference rules
        inference_ttl = """
        @prefix ex: <http://example.org/> .
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix owl: <http://www.w3.org/2002/07/owl#> .
        
        ex:EfficientBuilding rdfs:subClassOf ex:Building .
        
        ex:Building0 a ex:EfficientBuilding .
        """
        
        success = await dependencies.graphdb_client.load_ttl_data(inference_ttl)
        assert success
        
        # Query using inference
        inference_query = """
        PREFIX ex: <http://example.org/>
        
        SELECT ?building WHERE {
            ?building a ex:Building .
            ?building a ex:EfficientBuilding .
        }
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(inference_query)
        assert result is not None
        
        bindings = result["results"]["bindings"]
        assert len(bindings) > 0
    
    @pytest.mark.asyncio
    async def test_large_dataset_performance(
        self, 
        dependencies
    ):
        """Test performance with larger RDF datasets."""
        # Generate larger TTL dataset
        large_ttl = TestDataGenerator.generate_ttl_data(entity_count=100)
        
        # Load large dataset
        success = await dependencies.graphdb_client.load_ttl_data(large_ttl)
        assert success
        
        # Test query performance
        import time
        start_time = time.time()
        
        performance_query = """
        PREFIX ex: <http://example.org/>
        PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
        
        SELECT ?building ?label ?rating WHERE {
            ?building a ex:Building ;
                      rdfs:label ?label ;
                      ex:energyRating ?rating .
        }
        ORDER BY ?label
        """
        
        result = await dependencies.graphdb_client.execute_sparql_query(performance_query)
        end_time = time.time()
        
        query_time = end_time - start_time
        
        assert result is not None
        assert len(result["results"]["bindings"]) == 100
        # Query should complete within reasonable time (adjust threshold as needed)
        assert query_time < 10.0, f"Query took too long: {query_time}s"
    
    @pytest.mark.asyncio
    async def test_rdf_error_handling(
        self, 
        dependencies
    ):
        """Test error handling for invalid RDF operations."""
        # Invalid TTL data
        invalid_ttl = "This is not valid TTL data @#$%"
        
        success = await dependencies.graphdb_client.load_ttl_data(invalid_ttl)
        assert success is False
        
        # Invalid SPARQL query
        invalid_sparql = "INVALID SPARQL QUERY"
        
        result = await dependencies.graphdb_client.execute_sparql_query(invalid_sparql)
        # Should handle error gracefully (implementation dependent)
        # Either return None or error structure
        assert result is None or "error" in str(result).lower()
    
    @pytest.mark.asyncio
    async def test_rdf_transaction_handling(
        self, 
        dependencies, 
        sample_ttl_data: str
    ):
        """Test RDF transaction handling and rollback."""
        # This test depends on GraphDB transaction support
        # Implementation may vary based on GraphDB configuration
        
        # Get initial count
        count_query = """
        PREFIX ex: <http://example.org/>
        SELECT (COUNT(?building) as ?count) WHERE {
            ?building a ex:Building .
        }
        """
        
        initial_result = await dependencies.graphdb_client.execute_sparql_query(count_query)
        initial_count = int(initial_result["results"]["bindings"][0]["count"]["value"])
        
        # Try to add data that might fail
        try:
            new_ttl = """
            @prefix ex: <http://example.org/> .
            ex:TransactionTestBuilding a ex:Building .
            """
            
            success = await dependencies.graphdb_client.load_ttl_data(new_ttl)
            
            # Verify count increased
            final_result = await dependencies.graphdb_client.execute_sparql_query(count_query)
            final_count = int(final_result["results"]["bindings"][0]["count"]["value"])
            
            if success:
                assert final_count == initial_count + 1
            else:
                assert final_count == initial_count
                
        except Exception as e:
            # Transaction should rollback on error
            final_result = await dependencies.graphdb_client.execute_sparql_query(count_query)
            final_count = int(final_result["results"]["bindings"][0]["count"]["value"])
            assert final_count == initial_count

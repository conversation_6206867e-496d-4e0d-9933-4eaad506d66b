#!/usr/bin/env python3
"""
Service availability checker for the RDF Agent System.
Checks if all required external services are available and properly configured.
"""
import asyncio
import logging
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.dependencies import get_dependencies, cleanup_dependencies

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServiceChecker:
    """Check availability of all required services."""
    
    def __init__(self):
        self.dependencies = None
        self.service_status = {
            "kafka": False,
            "graphdb": False,
            "minio": False,
            "qdrant": False,
            "database": False
        }
    
    async def setup(self):
        """Set up dependencies."""
        try:
            self.dependencies = await get_dependencies()
            return True
        except Exception as e:
            logger.error(f"Failed to set up dependencies: {e}")
            return False
    
    async def cleanup(self):
        """Clean up dependencies."""
        try:
            if self.dependencies:
                await cleanup_dependencies()
        except Exception as e:
            logger.error(f"Failed to clean up dependencies: {e}")
    
    async def check_kafka(self) -> bool:
        """Check Kafka service availability."""
        logger.info("Checking Kafka service...")
        
        try:
            # Try to initialize Kafka client
            await self.dependencies.kafka_client.initialize()
            
            # Try to produce a test message
            test_message = {"test": "message", "timestamp": "2024-01-01T00:00:00Z"}
            await self.dependencies.kafka_client.produce_message("test_topic", test_message)
            
            logger.info("✅ Kafka service is available")
            self.service_status["kafka"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Kafka service is not available: {e}")
            return False
    
    async def check_graphdb(self) -> bool:
        """Check GraphDB service availability."""
        logger.info("Checking GraphDB service...")
        
        try:
            # Try to execute a simple SPARQL query
            query = "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 1"
            result = await self.dependencies.graphdb_client.execute_sparql_query(query)
            
            if result is not None:
                logger.info("✅ GraphDB service is available")
                self.service_status["graphdb"] = True
                return True
            else:
                logger.error("❌ GraphDB service returned no result")
                return False
                
        except Exception as e:
            logger.error(f"❌ GraphDB service is not available: {e}")
            return False
    
    async def check_minio(self) -> bool:
        """Check MinIO service availability."""
        logger.info("Checking MinIO service...")
        
        try:
            # Try to list objects (this will test connectivity)
            objects = await self.dependencies.minio_client.list_objects()
            
            # Try to upload a test file
            test_content = b"test content"
            test_path = "test/service_check.txt"
            
            upload_success = await self.dependencies.minio_client.upload_data(test_content, test_path)
            
            if upload_success:
                # Clean up test file
                await self.dependencies.minio_client.delete_object(test_path)
                
                logger.info("✅ MinIO service is available")
                self.service_status["minio"] = True
                return True
            else:
                logger.error("❌ MinIO service upload test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ MinIO service is not available: {e}")
            return False
    
    async def check_qdrant(self) -> bool:
        """Check Qdrant service availability."""
        logger.info("Checking Qdrant service...")
        
        try:
            # Try to get collection info or count points
            point_count = await self.dependencies.qdrant_client.count_points()
            
            logger.info(f"✅ Qdrant service is available (points: {point_count})")
            self.service_status["qdrant"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ Qdrant service is not available: {e}")
            return False
    
    async def check_database(self) -> bool:
        """Check database availability."""
        logger.info("Checking database service...")
        
        try:
            # Try to initialize database
            if self.dependencies.database_manager:
                # Database is already initialized during dependency setup
                logger.info("✅ Database service is available")
                self.service_status["database"] = True
                return True
            else:
                logger.error("❌ Database service is not available")
                return False
                
        except Exception as e:
            logger.error(f"❌ Database service is not available: {e}")
            return False
    
    async def check_all_services(self) -> bool:
        """Check all services."""
        logger.info("Checking all services...")
        logger.info("=" * 60)
        
        # Setup dependencies
        if not await self.setup():
            return False
        
        try:
            # Check each service
            await self.check_kafka()
            await self.check_graphdb()
            await self.check_minio()
            await self.check_qdrant()
            await self.check_database()
            
            # Print summary
            logger.info("=" * 60)
            logger.info("SERVICE STATUS SUMMARY")
            logger.info("=" * 60)
            
            for service, status in self.service_status.items():
                status_icon = "✅" if status else "❌"
                logger.info(f"{service.upper()}: {status_icon}")
            
            # Overall status
            available_services = sum(self.service_status.values())
            total_services = len(self.service_status)
            
            logger.info("=" * 60)
            logger.info(f"AVAILABLE SERVICES: {available_services}/{total_services}")
            
            if available_services == total_services:
                logger.info("🎉 All services are available!")
                return True
            elif available_services >= 3:  # At least 3 services should be available for basic functionality
                logger.warning("⚠️  Some services are unavailable, but system can still function")
                return True
            else:
                logger.error("❌ Too many services are unavailable")
                return False
                
        finally:
            await self.cleanup()
    
    def get_service_recommendations(self):
        """Get recommendations for missing services."""
        logger.info("=" * 60)
        logger.info("SERVICE SETUP RECOMMENDATIONS")
        logger.info("=" * 60)
        
        if not self.service_status["kafka"]:
            logger.info("📋 Kafka Setup:")
            logger.info("   - Ensure Kafka is running: docker compose up -d")
            logger.info("   - Check Kafka is accessible on localhost:9092")
        
        if not self.service_status["graphdb"]:
            logger.info("📋 GraphDB Setup:")
            logger.info("   - Install and start GraphDB")
            logger.info("   - Ensure GraphDB is accessible on localhost:7200")
            logger.info("   - Create repository 'rdf_agent_db'")
        
        if not self.service_status["minio"]:
            logger.info("📋 MinIO Setup:")
            logger.info("   - Install and start MinIO")
            logger.info("   - Ensure MinIO is accessible on localhost:9000")
            logger.info("   - Create bucket 'data'")
        
        if not self.service_status["qdrant"]:
            logger.info("📋 Qdrant Setup:")
            logger.info("   - Install and start Qdrant")
            logger.info("   - Ensure Qdrant is accessible on localhost:6333")
        
        if not self.service_status["database"]:
            logger.info("📋 Database Setup:")
            logger.info("   - Database should be automatically created (SQLite)")
            logger.info("   - Check file permissions in the backend directory")


async def main():
    """Main service checking function."""
    logger.info("Starting service availability check...")
    
    checker = ServiceChecker()
    
    try:
        success = await checker.check_all_services()
        
        if not success:
            checker.get_service_recommendations()
        
        if success:
            logger.info("🎉 Service check completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Service check failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Service check interrupted by user")
        await checker.cleanup()
        sys.exit(1)
    except Exception as e:
        logger.error(f"Service check failed: {e}")
        await checker.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

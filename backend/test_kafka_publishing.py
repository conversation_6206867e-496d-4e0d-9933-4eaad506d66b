#!/usr/bin/env python3
"""
Test script for Kafka message publishing using the existing tx.py script.
This script publishes sample data to Kafka topics for testing.
"""
import asyncio
import json
import logging
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KafkaPublishingTester:
    """Test Kafka message publishing functionality."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tx_script = self.project_root / ".context" / "kafka" / "tx.py"
        self.example_data_file = self.project_root / ".context" / "example_files" / "example_input.json"
    
    def check_prerequisites(self) -> bool:
        """Check if required files exist."""
        logger.info("Checking prerequisites...")
        
        if not self.tx_script.exists():
            logger.error(f"Kafka tx.py script not found at: {self.tx_script}")
            return False
        
        if not self.example_data_file.exists():
            logger.error(f"Example data file not found at: {self.example_data_file}")
            return False
        
        logger.info("Prerequisites check passed")
        return True
    
    def load_sample_data(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Load sample data from the example file."""
        logger.info(f"Loading sample data (limit: {limit})...")
        
        try:
            with open(self.example_data_file, 'r') as f:
                data = json.load(f)
            
            # Limit the data to avoid long processing times
            limited_data = data[:limit]
            logger.info(f"Loaded {len(limited_data)} sample items")
            return limited_data
            
        except Exception as e:
            logger.error(f"Failed to load sample data: {e}")
            return []
    
    def create_test_data_file(self, data: List[Dict[str, Any]], filename: str = "test_data.json") -> Path:
        """Create a temporary test data file."""
        test_file = Path("/tmp") / filename
        
        try:
            with open(test_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Created test data file: {test_file}")
            return test_file
            
        except Exception as e:
            logger.error(f"Failed to create test data file: {e}")
            raise
    
    def publish_to_kafka_topics(self, data: List[Dict[str, Any]]) -> bool:
        """Publish data to different Kafka topics based on content."""
        logger.info("Publishing data to Kafka topics...")
        
        try:
            # Separate data by type
            building_data = []
            address_data = []
            document_data = []
            
            for item in data:
                for metadata_item in item.get("graphMetadata", []):
                    class_type = metadata_item.get("classType", "")
                    
                    if "Building" in class_type:
                        building_message = {
                            "building_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        building_data.append(building_message)
                        
                    elif "Address" in class_type:
                        address_message = {
                            "address_id": metadata_item["id"],
                            "usecase_name": item.get("useCase", "TestCase"),
                            "properties": metadata_item.get("propertiesValues", {}),
                            "graph_data": item.get("graphData", ""),
                            "metadata": metadata_item
                        }
                        address_data.append(address_message)
                
                # Check for document references
                for metadata_item in item.get("graphMetadata", []):
                    properties = metadata_item.get("propertiesValues", {})
                    if "Certificates" in properties:
                        for cert in properties["Certificates"]:
                            if "pdf-path" in cert:
                                document_message = {
                                    "usecase_name": item.get("useCase", "TestCase"),
                                    "folders": cert["pdf-path"].split("/")[:-1],
                                    "filename": cert["pdf-path"].split("/")[-1],
                                    "object_path": cert["pdf-path"]
                                }
                                document_data.append(document_message)
            
            # Publish to different topics
            success_count = 0
            
            if building_data:
                if self.publish_messages_to_topic("building_data", building_data):
                    success_count += 1
            
            if address_data:
                if self.publish_messages_to_topic("address_data", address_data):
                    success_count += 1
            
            if document_data:
                if self.publish_messages_to_topic("document_processing", document_data):
                    success_count += 1
            
            logger.info(f"Successfully published to {success_count} topics")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Failed to publish to Kafka topics: {e}")
            return False
    
    def publish_messages_to_topic(self, topic: str, messages: List[Dict[str, Any]]) -> bool:
        """Publish messages to a specific Kafka topic."""
        logger.info(f"Publishing {len(messages)} messages to topic: {topic}")
        
        try:
            # Create temporary file for this topic's data
            topic_file = self.create_test_data_file(messages, f"{topic}_test.json")
            
            # Use kafka-python to publish messages
            from kafka import KafkaProducer
            
            producer = KafkaProducer(
                bootstrap_servers=['localhost:9092'],
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                max_block_ms=10000,
                request_timeout_ms=10000,
                retries=1
            )
            
            published_count = 0
            for message in messages:
                try:
                    producer.send(topic, message)
                    published_count += 1
                    logger.debug(f"Published message to {topic}")
                except Exception as e:
                    logger.error(f"Failed to publish message to {topic}: {e}")
            
            # Ensure all messages are sent
            producer.flush()
            producer.close()
            
            logger.info(f"Published {published_count}/{len(messages)} messages to {topic}")
            
            # Clean up temporary file
            topic_file.unlink(missing_ok=True)
            
            return published_count > 0
            
        except Exception as e:
            logger.error(f"Failed to publish to topic {topic}: {e}")
            return False
    
    def test_original_tx_script(self) -> bool:
        """Test the original tx.py script."""
        logger.info("Testing original tx.py script...")
        
        try:
            # Run the original tx.py script
            result = subprocess.run(
                [sys.executable, str(self.tx_script)],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info("Original tx.py script executed successfully")
                logger.info(f"Output: {result.stdout}")
                return True
            else:
                logger.error(f"Original tx.py script failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Original tx.py script timed out")
            return False
        except Exception as e:
            logger.error(f"Failed to run original tx.py script: {e}")
            return False
    
    def run_kafka_publishing_test(self) -> bool:
        """Run comprehensive Kafka publishing test."""
        logger.info("Starting Kafka publishing test...")
        
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Test original tx.py script first
            logger.info("=" * 50)
            logger.info("PHASE 1: Testing original tx.py script")
            original_success = self.test_original_tx_script()
            
            # Load sample data
            logger.info("=" * 50)
            logger.info("PHASE 2: Publishing structured test data")
            sample_data = self.load_sample_data(limit=3)  # Limit to 3 items for testing
            
            if not sample_data:
                logger.error("No sample data available")
                return False
            
            # Publish structured data to topics
            structured_success = self.publish_to_kafka_topics(sample_data)
            
            # Overall success
            overall_success = original_success or structured_success
            
            logger.info("=" * 50)
            logger.info("KAFKA PUBLISHING TEST RESULTS")
            logger.info("=" * 50)
            logger.info(f"Original tx.py script: {'✅ PASS' if original_success else '❌ FAIL'}")
            logger.info(f"Structured data publishing: {'✅ PASS' if structured_success else '❌ FAIL'}")
            logger.info(f"Overall: {'✅ PASS' if overall_success else '❌ FAIL'}")
            
            return overall_success
            
        except Exception as e:
            logger.error(f"Kafka publishing test failed: {e}")
            return False


def main():
    """Main test execution function."""
    logger.info("Starting Kafka publishing test...")
    
    tester = KafkaPublishingTester()
    
    try:
        success = tester.run_kafka_publishing_test()
        
        if success:
            logger.info("🎉 Kafka publishing test passed!")
            sys.exit(0)
        else:
            logger.error("❌ Kafka publishing test failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

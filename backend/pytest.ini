[tool:pytest]
# Pytest configuration for RDF Agent System

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, require services)
    e2e: End-to-end tests (slowest, full system)
    slow: Slow running tests
    requires_services: Tests that require external services
    requires_ai: Tests that require AI model access

# Async support
asyncio_mode = auto

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

# Minimum version
minversion = 7.0

# Test timeout (in seconds)
timeout = 300

# Ignore patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    htmlcov

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*

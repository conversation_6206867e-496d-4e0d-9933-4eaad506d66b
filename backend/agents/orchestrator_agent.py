"""
Orchestrator agent for coordinating RDF queries and RAG operations.
Main entry point for user queries and task coordination.
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

from core.dependencies import Dependencies
from models.data_models import QueryRequest, QueryResponse
from agents.rdf_query_agent import RDFQueryAgent
from agents.rag_agent import RAGAgent


logger = logging.getLogger(__name__)


class OrchestratorContext(BaseModel):
    """Context for the orchestrator agent."""
    session_id: Optional[str] = None
    user_query: str
    query_type: str = "natural_language"
    conversation_history: List[Dict[str, str]] = []


class OrchestratorAgent:
    """Main orchestrator agent for coordinating queries and responses."""
    
    def __init__(self, dependencies: Dependencies):
        self.deps = dependencies
        self.rdf_agent = RDFQueryAgent(dependencies)
        self.rag_agent = RAGAgent(dependencies)
        
        # Create PydanticAI agent with OpenRouter provider
        provider = OpenRouterProvider(api_key=dependencies.settings.ai.or_api_key)
        model = OpenAIChatModel('openai/gpt-4.1-mini', provider=provider)
        self.agent = Agent(
            model,
            system_prompt=self._get_system_prompt()
        )
        
        # Register tools
        self._register_tools()

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the orchestrator agent."""
        return """
        You are an intelligent orchestrator agent for an RDF knowledge base system.
        Your role is to understand user queries and coordinate between specialized agents:
        
        1. RDF Query Agent: Handles SPARQL queries and RDF data operations
        2. RAG Agent: Handles document retrieval and semantic search
        
        For each user query, you should:
        1. Analyze the query intent and determine which agents to involve
        2. Coordinate between agents to gather comprehensive information
        3. Synthesize responses from multiple agents into a coherent answer
        4. Provide clear, helpful responses with proper citations
        
        You have access to building and address data in RDF format, as well as 
        processed documents with semantic search capabilities.
        
        Always be helpful, accurate, and cite your sources when providing information.
        """

    def _register_tools(self):
        """Register tools for the orchestrator agent."""
        
        @self.agent.tool
        async def query_rdf_database(ctx: RunContext[OrchestratorContext], 
                                   query: str, 
                                   query_type: str = "natural_language") -> Dict[str, Any]:
            """Query the RDF database using the RDF Query Agent."""
            try:
                result = await self.rdf_agent.process_query(
                    query=query,
                    query_type=query_type,
                    session_id=ctx.deps.session_id
                )
                return {
                    "success": True,
                    "result": result,
                    "source": "rdf_database"
                }
            except Exception as e:
                logger.error(f"Error querying RDF database: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "source": "rdf_database"
                }

        @self.agent.tool
        async def search_documents(ctx: RunContext[OrchestratorContext], 
                                 query: str, 
                                 limit: int = 5) -> Dict[str, Any]:
            """Search documents using the RAG Agent."""
            try:
                result = await self.rag_agent.search_documents(
                    query=query,
                    limit=limit,
                    session_id=ctx.deps.session_id
                )
                return {
                    "success": True,
                    "result": result,
                    "source": "document_search"
                }
            except Exception as e:
                logger.error(f"Error searching documents: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "source": "document_search"
                }

        @self.agent.tool
        async def get_conversation_context(ctx: RunContext[OrchestratorContext]) -> Dict[str, Any]:
            """Get conversation history for context."""
            try:
                if not ctx.deps.session_id:
                    return {"context": "No session context available"}
                
                context = await self.deps.dependencies.session_manager.get_recent_context(
                    ctx.deps.session_id, max_turns=5
                )
                return {
                    "success": True,
                    "context": context,
                    "source": "conversation_history"
                }
            except Exception as e:
                logger.error(f"Error getting conversation context: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "source": "conversation_history"
                }

    async def process_query(self, query_request: QueryRequest) -> QueryResponse:
        """Process a user query and return a comprehensive response."""
        start_time = datetime.utcnow()
        
        try:
            # Get conversation history if session exists
            conversation_history = []
            if query_request.session_id:
                try:
                    turns = await self.deps.session_manager.get_conversation_history(
                        query_request.session_id, limit=5
                    )
                    conversation_history = [
                        {"role": "user", "content": turn.user_message}
                        for turn in turns
                    ]
                except Exception as e:
                    logger.warning(f"Could not get conversation history: {e}")
            
            # Create context
            context = OrchestratorContext(
                dependencies=self.deps,
                session_id=query_request.session_id,
                user_query=query_request.query_text,
                query_type=query_request.query_type,
                conversation_history=conversation_history
            )
            
            # Run the agent
            result = await self.agent.run(
                query_request.query_text,
                deps=context
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create response
            response = QueryResponse(
                query_id=query_request.query_id,
                session_id=query_request.session_id,
                response_text=result.output,
                processing_time=processing_time,
                timestamp=datetime.utcnow()
            )
            
            # Save conversation turn if session exists
            if query_request.session_id:
                try:
                    await self.deps.session_manager.add_conversation_turn(
                        session_id=query_request.session_id,
                        user_message=query_request.query_text,
                        assistant_response=result.output
                    )
                except Exception as e:
                    logger.warning(f"Could not save conversation turn: {e}")
            
            logger.info(f"Processed query {query_request.query_id} in {processing_time:.2f}s")
            return response
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return QueryResponse(
                query_id=query_request.query_id,
                session_id=query_request.session_id,
                response_text=f"I apologize, but I encountered an error while processing your query: {error_msg}",
                processing_time=processing_time,
                timestamp=datetime.utcnow()
            )

    async def health_check(self) -> bool:
        """Check if the orchestrator agent is healthy."""
        try:
            # Test basic agent functionality
            test_context = OrchestratorContext(
                dependencies=self.deps,
                user_query="test",
                query_type="natural_language"
            )
            
            # This is a simple test - in production you might want more comprehensive checks
            return True
            
        except Exception as e:
            logger.error(f"Orchestrator agent health check failed: {e}")
            return False

[project]
name = "rdf-agent-backend"
version = "0.1.0"
description = "Agentic AI Backend for RDF Database Querying with Document Processing"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",

    # AI and Agents
    "pydantic-ai>=0.0.49",
    "openai>=1.0.0",
    "anthropic>=0.68.0",

    # Kafka
    "aiokafka>=0.10.0",
    "kafka-python>=2.2.15",

    # Document Processing
    "docling>=2.0.0",

    # RDF and SPARQL
    "rdflib>=7.0.0",
    "SPARQLWrapper>=2.0.0",

    # Object Storage
    "minio>=7.2.0",
    "aiofiles>=23.0.0",

    # Vector Database
    "qdrant-client>=1.7.0",
    "sentence-transformers>=2.2.0",

    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "aiosqlite>=0.19.0",

    # HTTP Client
    "httpx>=0.25.0",

    # Configuration
    "pyyaml>=6.0.0",
    "python-dotenv>=1.0.0",

    # Utilities
    "rich>=13.0.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "pytest-timeout>=2.2.0",
    "pytest-xdist>=3.5.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]

[project.scripts]
rdf-agent = "main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = [
    "config",
    "core",
    "models",
    "agents",
    "services",
    "api",
    "infrastructure",
    "utils"
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[dependency-groups]
dev = [
    "pytest-asyncio>=1.2.0",
    "pytest-cov>=7.0.0",
]
